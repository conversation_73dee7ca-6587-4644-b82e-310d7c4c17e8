<div>
    <main class="nxl-container">
        <div class="nxl-content">
            <div class="page-header">
                <div class="page-header-left d-flex align-items-center">
                    <div class="page-header-title">
                        <h5 class="m-b-10">{{ __('create_page') }}</h5>
                    </div>
                    <ul class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('home') }}</a></li>
                        <li class="breadcrumb-item"><a href="{{ route('pages.index') }}">{{ __('pages') }}</a></li>
                        <li class="breadcrumb-item">{{ __('create') }}</li>
                    </ul>
                </div>
                <div class="page-header-right ms-auto">
                    <div class="page-header-right-items">
                        <div class="d-flex align-items-center gap-2 page-header-right-items-wrapper">
                            <button type="button" class="btn btn-secondary" wire:click="cancel">
                                <i class="feather-x me-2"></i>
                                {{ __('cancel') }}
                            </button>
                            <button type="button" class="btn btn-warning" wire:click="saveAsDraft">
                                <i class="feather-save me-2"></i>
                                {{ __('save_as_draft') }}
                            </button>
                            <button type="button" class="btn btn-primary" wire:click="saveAndPublish">
                                <i class="feather-check me-2"></i>
                                {{ __('save_and_publish') }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="main-content">
                <form wire:submit.prevent="store">
                    <div class="row">
                        <!-- Main Content -->
                        <div class="col-lg-8">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">{{ __('page_content') }}</h5>
                                    <!-- Language Tabs -->
                                    <div class="mt-3">
                                        <ul class="nav nav-tabs" role="tablist">
                                            @foreach($availableLanguages as $language)
                                                <li class="nav-item" role="presentation">
                                                    <button class="nav-link {{ $currentLanguage === $language->code ? 'active' : '' }}"
                                                            type="button"
                                                            wire:click="switchLanguage('{{ $language->code }}')">
                                                        {{ $language->flag }} {{ $language->name }}
                                                    </button>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                </div>
                                <div class="card-body">
                                    <!-- Title -->
                                    <div class="mb-3">
                                        <label for="title" class="form-label">{{ __('title') }} <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control @error('title') is-invalid @enderror"
                                               id="title" wire:model.live="title" placeholder="{{ __('enter_page_title') }}">
                                        @error('title')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Slug -->
                                    <div class="mb-3">
                                        <label for="slug" class="form-label">{{ __('slug') }}</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control @error('slug') is-invalid @enderror"
                                                   id="slug" wire:model="slug" placeholder="{{ __('enter_slug') }}">
                                            <button type="button" class="btn btn-outline-secondary" wire:click="generateSlug"
                                                    title="{{ __('generate_slug') }}">
                                                <i class="feather-refresh-cw"></i>
                                            </button>
                                        </div>
                                        <div class="form-text">{{ __('slug_help') }}</div>
                                        @error('slug')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Content -->
                                    <div class="mb-3">
                                        <label for="content" class="form-label">{{ __('content') }} <span class="text-danger">*</span></label>
                                        <div wire:ignore>
                                            <div id="content-editor" style="height: 300px;"></div>
                                        </div>
                                        <textarea class="d-none @error('content') is-invalid @enderror"
                                                  id="content" wire:model="content"
                                                  placeholder="{{ __('enter_page_content') }}"></textarea>
                                        @error('content')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Excerpt -->
                                    <div class="mb-3">
                                        <label for="excerpt" class="form-label">{{ __('excerpt') }}</label>
                                        <textarea class="form-control @error('excerpt') is-invalid @enderror"
                                                  id="excerpt" wire:model="excerpt" rows="3"
                                                  placeholder="{{ __('enter_page_excerpt') }}"></textarea>
                                        <div class="form-text">{{ __('excerpt_help') }}</div>
                                        @error('excerpt')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- SEO Settings -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title">{{ __('seo_settings') }}</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Meta Title -->
                                    <div class="mb-3">
                                        <label for="meta_title" class="form-label">{{ __('meta_title') }}</label>
                                        <input type="text" class="form-control @error('meta_title') is-invalid @enderror"
                                               id="meta_title" wire:model="meta_title" placeholder="{{ __('enter_meta_title') }}">
                                        @error('meta_title')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Meta Description -->
                                    <div class="mb-3">
                                        <label for="meta_description" class="form-label">{{ __('meta_description') }}</label>
                                        <textarea class="form-control @error('meta_description') is-invalid @enderror"
                                                  id="meta_description" wire:model="meta_description" rows="3"
                                                  placeholder="{{ __('enter_meta_description') }}"></textarea>
                                        @error('meta_description')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Meta Keywords -->
                                    <div class="mb-3">
                                        <label class="form-label">{{ __('meta_keywords') }}</label>
                                        @foreach($meta_keywords as $index => $keyword)
                                            <div class="input-group mb-2">
                                                <input type="text" class="form-control"
                                                       wire:model="meta_keywords.{{ $index }}"
                                                       placeholder="{{ __('enter_keyword') }}">
                                                <button type="button" class="btn btn-outline-danger"
                                                        wire:click="removeKeyword({{ $index }})">
                                                    <i class="feather-x"></i>
                                                </button>
                                            </div>
                                        @endforeach
                                        <button type="button" class="btn btn-outline-primary btn-sm" wire:click="addKeyword">
                                            <i class="feather-plus me-1"></i> {{ __('add_keyword') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Sidebar -->
                        <div class="col-lg-4">
                            <!-- Publish Settings -->
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="card-title">{{ __('publish_settings') }}</h5>
                                </div>
                                <div class="card-body">
                                    <!-- Status -->
                                    <div class="mb-3">
                                        <label for="status" class="form-label">{{ __('status') }}</label>
                                        <select class="form-select @error('status') is-invalid @enderror"
                                                id="status" wire:model="status">
                                            <option value="draft">{{ __('draft') }}</option>
                                            <option value="published">{{ __('published') }}</option>
                                            <option value="archived">{{ __('archived') }}</option>
                                        </select>
                                        @error('status')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Published At -->
                                    <div class="mb-3">
                                        <label for="published_at" class="form-label">{{ __('published_at') }}</label>
                                        <input type="datetime-local" class="form-control @error('published_at') is-invalid @enderror"
                                               id="published_at" wire:model="published_at">
                                        <div class="form-text">{{ __('publish_date_help') }}</div>
                                        @error('published_at')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>

                                    <!-- Featured -->
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox"
                                                   id="is_featured" wire:model="is_featured">
                                            <label class="form-check-label" for="is_featured">
                                                {{ __('is_featured') }}
                                            </label>
                                        </div>
                                        <div class="form-text">{{ __('featured_help') }}</div>
                                    </div>

                                    <!-- Sort Order -->
                                    <div class="mb-3">
                                        <label for="sort_order" class="form-label">{{ __('sort_order') }}</label>
                                        <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                                               id="sort_order" wire:model="sort_order" min="0">
                                        <div class="form-text">{{ __('sort_order_help') }}</div>
                                        @error('sort_order')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Featured Image -->
                            <div class="card mt-3">
                                <div class="card-header">
                                    <h5 class="card-title">{{ __('featured_image') }}</h5>
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <input type="text" class="form-control @error('featured_image') is-invalid @enderror"
                                               wire:model="featured_image" placeholder="{{ __('image_url') }}">
                                        @error('featured_image')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                    </div>
                                    @if($featured_image)
                                        <div class="text-center">
                                            <img src="{{ $featured_image }}" alt="Featured Image"
                                                 class="img-fluid rounded" style="max-height: 200px;">
                                        </div>
                                    @endif
                                </div>
                            </div>

                            <!-- Actions -->
                            <div class="card mt-3">
                                <div class="card-body">
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-outline-secondary" wire:click="preview">
                                            <i class="feather-eye me-2"></i>
                                            {{ __('preview') }}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </main>
</div>

@push('styles')
<link rel="stylesheet" type="text/css" href="{{ asset('assets/vendors/css/quill.min.css') }}">
@endpush

@push('scripts')
<script src="{{ asset('assets/vendors/js/quill.min.js') }}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Quill Editor
    var quill = new Quill('#content-editor', {
        theme: 'snow',
        modules: {
            toolbar: [
                [{ 'header': [1, 2, 3, 4, 5, 6, false] }],
                ['bold', 'italic', 'underline', 'strike'],
                [{ 'color': [] }, { 'background': [] }],
                [{ 'align': [] }],
                [{ 'list': 'ordered'}, { 'list': 'bullet' }],
                [{ 'indent': '-1'}, { 'indent': '+1' }],
                ['link', 'image', 'video'],
                ['clean']
            ]
        },
        placeholder: '{{ __("enter_page_content") }}'
    });

    // Set initial content if exists
    var initialContent = @json($content ?? '');
    if (initialContent) {
        quill.root.innerHTML = initialContent;
    }

    // Sync Quill content with Livewire
    quill.on('text-change', function() {
        var content = quill.root.innerHTML;
        @this.set('content', content);
    });

    // Listen for Livewire updates
    Livewire.on('contentUpdated', function(content) {
        quill.root.innerHTML = content || '';
    });

    // Listen for language switch
    Livewire.on('languageSwitched', function(content) {
        quill.root.innerHTML = content || '';
    });
});
</script>
@endpush
