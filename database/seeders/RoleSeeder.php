<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleSeeder extends Seeder
{
    public function run(): void
    {
        // إنشاء الأدوار
        $adminRole = Role::create(['name' => 'admin']);
        $userRole = Role::create(['name' => 'user']);

        // إعطاء جميع الصلاحيات للمدير
        $adminRole->givePermissionTo(Permission::all());

        // إعطاء صلاحيات محدودة للمستخدم العادي
        $userRole->givePermissionTo(['users.view', 'languages.view']);
    }
}
