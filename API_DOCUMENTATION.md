# API Documentation - Transactions

## Overview
This API provides endpoints for managing and retrieving user transaction history (point transactions). Users can view their earned and spent points with various filtering options.

## Base URL
```
http://127.0.0.1:8000/api
```

## Authentication
All endpoints require authentication using Sanctum Bearer token.

**Header:**
```
Authorization: Bearer {your_token}
```

## Endpoints

### 1. Get All Transactions
**GET** `/transactions`

Retrieve user's transaction history with optional filtering.

**Query Parameters:**
- `type` (optional): Filter by transaction type (`earned` or `spent`)
- `per_page` (optional): Number of items per page (1-100, default: 15)
- `search` (optional): Search in transaction description or order reference
- `date_from` (optional): Start date filter (Y-m-d format)
- `date_to` (optional): End date filter (Y-m-d format)

**Example Request:**
```bash
curl -X GET "http://127.0.0.1:8000/api/transactions?type=earned&per_page=10&date_from=2025-01-01" \
  -H "Authorization: Bearer your_token_here"
```

**Response:**
```json
{
  "status": "success",
  "message": "تم جلب المعاملات بنجاح",
  "data": {
    "wallet_summary": {
      "balance": 170,
      "total_earned": 225,
      "total_spent": 55,
      "transactions_count": 10
    },
    "transactions": {
      "data": [
        {
          "id": 1,
          "type": "earned",
          "type_label": "إيرادات",
          "amount": 100,
          "formatted_amount": "100",
          "amount_with_sign": "+100",
          "description": "مكافأة التسجيل",
          "balance_after": 100,
          "formatted_balance_after": "100",
          "order_id": null,
          "order": null,
          "created_at": "2025-09-22T14:58:03.000000Z",
          "created_at_formatted": "2025-09-22 14:58",
          "created_at_human": "منذ ساعة",
          "created_at_date": "2025-09-22",
          "created_at_time": "14:58"
        }
      ],
      "current_page": 1,
      "last_page": 1,
      "per_page": 15,
      "total": 5,
      "from": 1,
      "to": 5
    }
  }
}
```

### 2. Get Earned Transactions Only
**GET** `/transactions/earned`

Retrieve only earned transactions. Accepts same query parameters as main endpoint except `type`.

**Example Request:**
```bash
curl -X GET "http://127.0.0.1:8000/api/transactions/earned?per_page=5" \
  -H "Authorization: Bearer your_token_here"
```

### 3. Get Spent Transactions Only
**GET** `/transactions/spent`

Retrieve only spent transactions. Accepts same query parameters as main endpoint except `type`.

**Example Request:**
```bash
curl -X GET "http://127.0.0.1:8000/api/transactions/spent?per_page=5" \
  -H "Authorization: Bearer your_token_here"
```

### 4. Get Transaction Details
**GET** `/transactions/{id}`

Retrieve detailed information about a specific transaction.

**Path Parameters:**
- `id`: Transaction ID

**Example Request:**
```bash
curl -X GET "http://127.0.0.1:8000/api/transactions/1" \
  -H "Authorization: Bearer your_token_here"
```

**Response:**
```json
{
  "status": "success",
  "message": "تم جلب المعاملة بنجاح",
  "data": {
    "id": 1,
    "type": "earned",
    "type_label": "إيرادات",
    "amount": 100,
    "formatted_amount": "100",
    "amount_with_sign": "+100",
    "description": "مكافأة التسجيل",
    "balance_after": 100,
    "formatted_balance_after": "100",
    "order_id": 5,
    "order": {
      "id": 5,
      "reference": "ORD-2025-001",
      "from_station": "محطة الرياض",
      "to_station": "محطة جدة",
      "type": "صندوق صغير"
    },
    "created_at": "2025-09-22T14:58:03.000000Z",
    "created_at_formatted": "2025-09-22 14:58",
    "created_at_human": "منذ ساعة",
    "created_at_date": "2025-09-22",
    "created_at_time": "14:58"
  }
}
```

### 5. Get Transaction Statistics
**GET** `/transactions/statistics`

Retrieve wallet summary and monthly statistics for the last 6 months.

**Example Request:**
```bash
curl -X GET "http://127.0.0.1:8000/api/transactions/statistics" \
  -H "Authorization: Bearer your_token_here"
```

**Response:**
```json
{
  "status": "success",
  "message": "تم جلب الإحصائيات بنجاح",
  "data": {
    "wallet_summary": {
      "balance": 170,
      "total_earned": 225,
      "total_spent": 55,
      "transactions_count": 10
    },
    "monthly_statistics": [
      {
        "month": "2025-04",
        "month_name": "April 2025",
        "earned": 150,
        "spent": 30,
        "net": 120
      },
      {
        "month": "2025-05",
        "month_name": "May 2025",
        "earned": 75,
        "spent": 25,
        "net": 50
      }
    ]
  }
}
```

## Error Responses

### Validation Error (422)
```json
{
  "status": "error",
  "message": "خطأ في البيانات المدخلة",
  "errors": {
    "type": ["The selected type is invalid."],
    "per_page": ["The per page must be between 1 and 100."]
  }
}
```

### Transaction Not Found (404)
```json
{
  "status": "error",
  "message": "المعاملة غير موجودة"
}
```

### Server Error (500)
```json
{
  "status": "error",
  "message": "حدث خطأ ما",
  "error": "Error details here"
}
```

## Transaction Types
- `earned`: Points earned by the user
- `spent`: Points spent by the user

## Notes
- All amounts are returned as integers (no decimal places)
- Dates are returned in ISO 8601 format
- The API supports both Arabic and English responses based on the `Accept-Language` header
- Pagination follows Laravel's standard pagination format
