<?php

namespace App\Services;

use App\Models\Station;

class StationService
{
    public function create(array $data)
    {
        return Station::create($data);
    }

    public function update(Station $station, array $data)
    {
        $station->update($data);
        return $station;
    }

    public function delete(Station $station)
    {
        return $station->delete();
    }

    public function getAll($perPage = 10, $withTrashed = false)
    {
        $query = Station::query();
        if ($withTrashed) {
            $query->onlyTrashed();
        }
        return $query->paginate($perPage);
    }

    public function restore(Station $station)
    {
        return $station->restore();
    }

    public function forceDelete(Station $station)
    {
        return $station->forceDelete();
    }

    public function findById($id)
    {
        return Station::withTrashed()->findOrFail($id);
    }
}