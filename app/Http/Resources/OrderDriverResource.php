<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderDriverResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'from_station' => new StationResource($this->fromStation),
            'to_station' => new StationResource($this->toStation),
            'type' => $this->type?->name,
            'status' => 'waiting_for_order',
            'status_text' => __('waiting_for_order'),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'is_interest' => true,
        ];
    }
}
