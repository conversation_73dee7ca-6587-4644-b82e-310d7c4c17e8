<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        // Get settings
        $settings = \App\Models\Setting::whereIn('key', ['cash_to_points_rate', 'delivery_deadline_minutes'])
            ->pluck('value', 'key');

        // Calculate points based on order price and cash_to_points_rate
        $pointsRate = (float) ($settings['cash_to_points_rate'] ?? 1);
        $points = (int) ($this->price * $pointsRate);

        // Get and calculate delivery deadline
        $deliveryDeadlineMinutes = (int) ($settings['delivery_deadline_minutes'] ?? 30);

        // Calculate the exact delivery deadline
        $deliveryDeadline = $this->created_at->copy()->addMinutes($deliveryDeadlineMinutes);
        $now = now();

        // Calculate remaining time in seconds
        $remainingSeconds = max(0, $now->diffInSeconds($deliveryDeadline, false));

        // If remaining time is negative (deadline passed), set to 0
        $remainingSeconds = max(0, $remainingSeconds);

        // Convert to minutes and seconds
        $minutes = (int) ($remainingSeconds / 60);
        $seconds = $remainingSeconds % 60;
        $formattedTime = $remainingSeconds > 0 ? sprintf('%02d:%02d', $minutes, $seconds) : '00:00';

        return [
            'id' => $this->id,
            'barcode' => $this->barcode,
            'from_station' => new StationResource($this->fromStation),
            'to_station' => new StationResource($this->toStation),
            'type' => $this->type?->name,
            'receiver_name' => $this->receiver_name,
            'receiver_phone' => $this->receiver_phone,
            'note' => $this->note,
            'price' => $this->price,
            'status' => $this->status,
            'status_text' => $this->getStatusMessage(auth()->id() === $this->user_id),
            'payment_method' => $this->payment_method,
            'points' => $points,
            'points_used' => $this->points_used,
            'delivery_deadline' => [
                'minutes' => $deliveryDeadlineMinutes,
                //'datetime' => $deliveryDeadline,
                'remaining_time' => $formattedTime,
            ],
            'created_at' => $this->created_at,
            'latest_tracking' => new TrackingResource($this->whenLoaded('latestTracking')),
        ];
    }

    /**
     * Get the appropriate status message based on order status and user role
     *
     * @param bool $isOwner Whether the current user is the order owner
     * @return string
     */
    private function getStatusMessage(bool $isOwner): string
    {
        $status = $this->status;
        $key = $isOwner ? 'owner' : 'other';
        
        // Try to get the translation from JSON file
        $translation = trans("order_status.{$key}.{$status}");
        
        // If translation is the same as the key, it means translation wasn't found
        if ($translation === "order_status.{$key}.{$status}") {
            // Try to get the translation from the other user type as fallback
            $fallbackKey = $isOwner ? 'other' : 'owner';
            $translation = trans("order_status.{$fallbackKey}.{$status}");
            
            // If still not found, try English locale
            if ($translation === "order_status.{$fallbackKey}.{$status}") {
                $translation = trans("order_status.{$key}.{$status}", [], 'en');
                
                // If still not found, return the status as is
                if ($translation === "order_status.{$key}.{$status}") {
                    return $status;
                }
            }
        }
        
        return $translation;
    }

    public function with(Request $request): array
    {
        return [
            'success' => $this->additional['success'] ?? true,
            'message' => $this->additional['message'] ?? '',
        ];
    }
}
