<?php

namespace App\Http\Controllers\Web\Types;

use App\Services\TypeService;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;
    
    protected $paginationTheme = 'bootstrap';

    protected TypeService $typeService;
    public $type;
    public $name;
    public $showCreateModal = false;
    public $showEditModal = false;
    public $showDeleteModal = false;
    public $showDeleted = false;

    public function boot(TypeService $typeService)
    {
        $this->typeService = $typeService;
    }

    public function create()
    {
        if (!auth()->user()->can('types.create')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }
        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function store()
    {
        if (!auth()->user()->can('types.create')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }
        
        $this->validate([
            'name' => 'required|string|max:255|unique:types,name',
        ]);

        $this->typeService->create(['name' => $this->name]);

        $this->showCreateModal = false;
        $this->resetForm();
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('type_created_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function edit($id)
    {
        if (!auth()->user()->can('types.edit')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }
        $this->type = $this->typeService->findById($id);
        $this->name = $this->type->name;
        $this->showEditModal = true;
    }

    public function update()
    {
        if (!auth()->user()->can('types.edit')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }
        
        $this->validate([
            'name' => 'required|string|max:255|unique:types,name,' . $this->type->id,
        ]);

        $this->typeService->update($this->type, ['name' => $this->name]);

        $this->showEditModal = false;
        $this->resetForm();
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('type_updated_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function confirmDelete($id)
    {
        if (!auth()->user()->can('types.delete')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }
        $this->type = $this->typeService->findById($id);
        $this->showDeleteModal = true;
    }

    public function delete()
    {
        if (!auth()->user()->can('types.delete')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }
        $this->typeService->delete($this->type);
        $this->showDeleteModal = false;
        $this->reset(['type']);
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('type_deleted_successfully'))->confirmButtonText(__('yes'))->show();
    }

    private function resetForm()
    {
        $this->reset(['name', 'type']);
    }

    public function closeModal()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->showDeleteModal = false;
        $this->showRestoreModal = false;
        $this->showForceDeleteModal = false;
        $this->resetForm();
    }

    public function render()
    {
        return view('pages.types.index', [
            'types' => $this->typeService->getAll(10, $this->showDeleted)
        ])->layout('layouts.app');
    }

    public $showRestoreModal = false;
    public $showForceDeleteModal = false;

    public function confirmRestore($id)
    {
        $this->type = $this->typeService->findById($id);
        $this->showRestoreModal = true;
    }

    public function restore()
    {
        $this->typeService->restore($this->type);
        $this->showRestoreModal = false;
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('type_restored_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function confirmForceDelete($id)
    {
        $this->type = $this->typeService->findById($id);
        $this->showForceDeleteModal = true;
    }

    public function forceDelete()
    {
        $this->typeService->forceDelete($this->type);
        $this->showForceDeleteModal = false;
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('type_permanently_deleted'))->confirmButtonText(__('yes'))->show();
    }
}