<?php

namespace App\Http\Controllers\Web\Stations;

use App\Services\StationService;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;
    
    protected $paginationTheme = 'bootstrap';

    protected StationService $stationService;
    public $station;
    public $name, $latitude, $longitude;
    public $showCreateModal = false;
    public $showEditModal = false;
    public $showDeleteModal = false;
    public $showDeleted = false;

    public function boot(StationService $stationService)
    {
        $this->stationService = $stationService;
    }

    public function create()
    {
        return redirect()->route('stations.create');
    }

    public function store()
    {
        if (!auth()->user()->can('stations.create')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }
        
        $this->validate([
            'name' => 'required|string|max:255|unique:stations,name',
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        $this->stationService->create([
            'name' => $this->name,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
        ]);

        $this->showCreateModal = false;
        $this->resetForm();
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('station_created_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function edit($id)
    {
        return redirect()->route('stations.edit', $id);
    }

    public function update()
    {
        if (!auth()->user()->can('stations.edit')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }
        
        $this->validate([
            'name' => 'required|string|max:255|unique:stations,name,' . $this->station->id,
            'latitude' => 'required|numeric|between:-90,90',
            'longitude' => 'required|numeric|between:-180,180',
        ]);

        $this->stationService->update($this->station, [
            'name' => $this->name,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
        ]);

        $this->showEditModal = false;
        $this->resetForm();
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('station_updated_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function confirmDelete($id)
    {
        if (!auth()->user()->can('stations.delete')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }
        $this->station = $this->stationService->findById($id);
        $this->showDeleteModal = true;
    }

    public function delete()
    {
        if (!auth()->user()->can('stations.delete')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }
        $this->stationService->delete($this->station);
        $this->showDeleteModal = false;
        $this->reset(['station']);
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('station_deleted_successfully'))->confirmButtonText(__('yes'))->show();
    }

    private function resetForm()
    {
        $this->reset(['name', 'latitude', 'longitude', 'station']);
    }

    public function closeModal()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->showDeleteModal = false;
        $this->showRestoreModal = false;
        $this->showForceDeleteModal = false;
        $this->resetForm();
    }

    public function render()
    {
        return view('pages.stations.index', [
            'stations' => $this->stationService->getAll(10, $this->showDeleted)
        ])->layout('layouts.app');
    }

    public $showRestoreModal = false;
    public $showForceDeleteModal = false;

    public function confirmRestore($id)
    {
        $this->station = $this->stationService->findById($id);
        $this->showRestoreModal = true;
    }

    public function restore()
    {
        $this->stationService->restore($this->station);
        $this->showRestoreModal = false;
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('station_restored_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function confirmForceDelete($id)
    {
        $this->station = $this->stationService->findById($id);
        $this->showForceDeleteModal = true;
    }

    public function forceDelete()
    {
        $this->stationService->forceDelete($this->station);
        $this->showForceDeleteModal = false;
        (new LivewireAlert($this))->success()->title(__('success'))->text(__('station_permanently_deleted'))->confirmButtonText(__('yes'))->show();
    }
}