<?php

namespace App\Http\Controllers\Web\Pages;

use App\Services\PageService;
use Livewire\Component;
use Livewire\WithPagination;
use <PERSON><PERSON><PERSON>ezo\LivewireAlert\LivewireAlert;

class Index extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';

    protected PageService $pageService;

    public $showCreateModal = false;
    public $showEditModal = false;
    public $showViewModal = false;
    public $showDeleteModal = false;
    public $showDeleted = false;
    public $selectedPageId;

    // Form fields
    public $title, $slug, $content, $excerpt;
    public $meta_title, $meta_description, $meta_keywords = [];
    public $featured_image, $status = 'draft', $is_featured = false;
    public $sort_order = 0, $published_at;

    // Filters
    public $statusFilter = 'all';
    public $search = '';

    public function boot(PageService $pageService)
    {
        $this->pageService = $pageService;
    }

    public function render()
    {
        $pages = $this->getPages();
        $statusCounts = $this->pageService->getStatusCounts();

        return view('pages.pages.index', [
            'pages' => $pages,
            'statusCounts' => $statusCounts,
            'statusOptions' => $this->pageService->getStatusOptions(),
        ]);
    }

    protected function getPages()
    {
        if ($this->showDeleted) {
            return $this->pageService->getAll(10, true);
        }

        if ($this->search) {
            return $this->pageService->search($this->search, 10);
        }

        if ($this->statusFilter !== 'all') {
            return $this->pageService->getByStatus($this->statusFilter, 10);
        }

        return $this->pageService->getAll(10);
    }

    public function create()
    {
        if (!auth()->user()->can('pages.create')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        $this->resetForm();
        $this->showCreateModal = true;
    }

    public function store()
    {
        if (!auth()->user()->can('pages.create')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        $this->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:pages,slug',
            'content' => 'required|string',
            'excerpt' => 'nullable|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'featured_image' => 'nullable|string',
            'status' => 'required|in:draft,published,archived',
            'is_featured' => 'boolean',
            'sort_order' => 'integer|min:0',
            'published_at' => 'nullable|date',
        ]);

        $data = [
            'title' => $this->title,
            'slug' => $this->slug,
            'content' => $this->content,
            'excerpt' => $this->excerpt,
            'meta_title' => $this->meta_title,
            'meta_description' => $this->meta_description,
            'meta_keywords' => $this->meta_keywords,
            'featured_image' => $this->featured_image,
            'status' => $this->status,
            'is_featured' => $this->is_featured,
            'sort_order' => $this->sort_order,
            'published_at' => $this->published_at,
        ];

        $this->pageService->create($data);

        $this->resetForm();
        $this->showCreateModal = false;

        (new LivewireAlert($this))->success()->title(__('success'))->text(__('page_created_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function edit($id)
    {
        if (!auth()->user()->can('pages.edit')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        $page = $this->pageService->findById($id);
        $this->selectedPageId = $id;
        $this->title = $page->title;
        $this->slug = $page->slug;
        $this->content = $page->content;
        $this->excerpt = $page->excerpt;
        $this->meta_title = $page->meta_title;
        $this->meta_description = $page->meta_description;
        $this->meta_keywords = $page->meta_keywords ?? [];
        $this->featured_image = $page->featured_image;
        $this->status = $page->status;
        $this->is_featured = $page->is_featured;
        $this->sort_order = $page->sort_order;
        $this->published_at = $page->published_at?->format('Y-m-d\TH:i');
        $this->showEditModal = true;
    }

    public function update()
    {
        if (!auth()->user()->can('pages.edit')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        $this->validate([
            'title' => 'required|string|max:255',
            'slug' => 'nullable|string|max:255|unique:pages,slug,' . $this->selectedPageId,
            'content' => 'required|string',
            'excerpt' => 'nullable|string',
            'meta_title' => 'nullable|string|max:255',
            'meta_description' => 'nullable|string|max:500',
            'featured_image' => 'nullable|string',
            'status' => 'required|in:draft,published,archived',
            'is_featured' => 'boolean',
            'sort_order' => 'integer|min:0',
            'published_at' => 'nullable|date',
        ]);

        $page = $this->pageService->findById($this->selectedPageId);

        $data = [
            'title' => $this->title,
            'slug' => $this->slug,
            'content' => $this->content,
            'excerpt' => $this->excerpt,
            'meta_title' => $this->meta_title,
            'meta_description' => $this->meta_description,
            'meta_keywords' => $this->meta_keywords,
            'featured_image' => $this->featured_image,
            'status' => $this->status,
            'is_featured' => $this->is_featured,
            'sort_order' => $this->sort_order,
            'published_at' => $this->published_at,
        ];

        $this->pageService->update($page, $data);

        $this->resetForm();
        $this->showEditModal = false;

        (new LivewireAlert($this))->success()->title(__('success'))->text(__('page_updated_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function show($id)
    {
        $this->selectedPageId = $id;
        $this->showViewModal = true;
    }

    public function confirmDelete($id)
    {
        if (!auth()->user()->can('pages.delete')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        $this->selectedPageId = $id;
        $this->showDeleteModal = true;
    }

    public function delete()
    {
        if (!auth()->user()->can('pages.delete')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        $page = $this->pageService->findById($this->selectedPageId);
        $this->pageService->delete($page);

        $this->showDeleteModal = false;
        $this->selectedPageId = null;

        (new LivewireAlert($this))->success()->title(__('success'))->text(__('page_deleted_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function restore($id)
    {
        if (!auth()->user()->can('pages.edit')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        $page = $this->pageService->findById($id, true);
        $this->pageService->restore($page);

        (new LivewireAlert($this))->success()->title(__('success'))->text(__('page_restored_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function toggleFeatured($id)
    {
        if (!auth()->user()->can('pages.edit')) {
            (new LivewireAlert($this))->error()->title(__('error'))->text(__('unauthorized'))->confirmButtonText(__('yes'))->show();
            return;
        }

        $page = $this->pageService->findById($id);
        $this->pageService->toggleFeatured($page);

        (new LivewireAlert($this))->success()->title(__('success'))->text(__('page_updated_successfully'))->confirmButtonText(__('yes'))->show();
    }

    public function resetForm()
    {
        $this->title = '';
        $this->slug = '';
        $this->content = '';
        $this->excerpt = '';
        $this->meta_title = '';
        $this->meta_description = '';
        $this->meta_keywords = [];
        $this->featured_image = '';
        $this->status = 'draft';
        $this->is_featured = false;
        $this->sort_order = 0;
        $this->published_at = '';
        $this->selectedPageId = null;
    }

    public function closeModal()
    {
        $this->showCreateModal = false;
        $this->showEditModal = false;
        $this->showViewModal = false;
        $this->showDeleteModal = false;
        $this->resetForm();
    }

    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedStatusFilter()
    {
        $this->resetPage();
    }

    public function toggleDeleted()
    {
        $this->showDeleted = !$this->showDeleted;
        $this->resetPage();
    }
}
