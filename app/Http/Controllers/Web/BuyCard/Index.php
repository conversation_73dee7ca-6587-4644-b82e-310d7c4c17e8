<?php

namespace App\Http\Controllers\Web\BuyCard;

use App\Models\Language;
use App\Services\UserService;
use App\Services\UserStatsService;
use Jantinnerezo\LivewireAlert\LivewireAlert;
use Livewire\Component;
use Livewire\WithPagination;

class Index extends Component
{
    use WithPagination;

    protected $paginationTheme = 'bootstrap';
    // Filter properties (same as Users/Index)
    public $search = '';
    public $selectedRole = '';
    public $dateFrom = '';
    public $dateTo = '';
    public $perPage = 10;

    // Buy card modal properties
    public $showBuyCardModal = false;
    public $selectedUser;
    public $userStats = [];
    public $cardName = '';
    public $cashAmount = '';
    public $pointsAmount = '';
    public $conversionRate = 0;

    protected $userService;
    protected $userStatsService;

    public function boot(UserService $userService, UserStatsService $userStatsService)
    {
        $this->userService = $userService;
        $this->userStatsService = $userStatsService;
    }

    public function mount()
    {
        // Check if user is admin
        if (!auth()->user()->hasRole('admin')) {
            abort(403, __('unauthorized'));
        }
    }

    // Filter methods (same as Users/Index)
    public function updatedSearch()
    {
        $this->resetPage();
    }

    public function updatedSelectedRole()
    {
        $this->resetPage();
    }

    public function updatedDateFrom()
    {
        $this->resetPage();
    }

    public function updatedDateTo()
    {
        $this->resetPage();
    }

    public function updatedPerPage()
    {
        $this->resetPage();
    }

    public function resetFilters()
    {
        $this->reset(['search', 'selectedRole', 'dateFrom', 'dateTo']);
        $this->resetPage();
    }

    public function getRoles()
    {
        return \Spatie\Permission\Models\Role::all();
    }

    // Buy card modal methods
    public function openBuyCardModal($userId)
    {
        $this->selectedUser = $userId;
        $this->userStats = $this->userStatsService->getUserStats($userId);
        $this->conversionRate = $this->userStats['conversion_rates']['points_to_cash_rate'];
        $this->reset(['cardName', 'cashAmount', 'pointsAmount']);
        $this->showBuyCardModal = true;
    }

    public function closeBuyCardModal()
    {
        $this->showBuyCardModal = false;
        $this->reset(['selectedUser', 'userStats', 'cardName', 'cashAmount', 'pointsAmount']);
    }

    // Real-time conversion methods
    public function updatedCashAmount()
    {
        if ($this->cashAmount && is_numeric($this->cashAmount)) {
            // Use points_to_cash_rate for converting cash to points
            $pointsToCashRate = $this->userStats['conversion_rates']['points_to_cash_rate'];
            $this->pointsAmount = $this->userStatsService->convertCashToPoints(
                (float) $this->cashAmount,
                $pointsToCashRate
            );
        } else {
            $this->pointsAmount = '';
        }
    }

    public function updatedPointsAmount()
    {
        if ($this->pointsAmount && is_numeric($this->pointsAmount)) {
            // Use points_to_cash_rate for converting points to cash
            $pointsToCashRate = $this->userStats['conversion_rates']['points_to_cash_rate'];
            $this->cashAmount = $this->userStatsService->convertPointsToCash(
                (int) $this->pointsAmount,
                $pointsToCashRate
            );
        } else {
            $this->cashAmount = '';
        }
    }

    // Process buy card
    public function buyCard()
    {
        $this->validate([
            'cardName' => 'required|string|max:255',
            'pointsAmount' => 'required|numeric|min:1',
        ], [
            'cardName.required' => __('card_name_required'),
            'pointsAmount.required' => __('points_amount_required'),
            'pointsAmount.min' => __('points_amount_min'),
        ]);

        try {
            $this->userStatsService->processBuyCard(
                $this->selectedUser,
                $this->cardName,
                (int) $this->pointsAmount
            );

            (new LivewireAlert($this))->success()->title(__('success'))->text(__('card_purchased_successfully'))->confirmButtonText(__('ok'))->show();

            $this->closeBuyCardModal();
        } catch (\Exception $e) {
            (new LivewireAlert($this))->error()->title(__('error'))->text($e->getMessage())->confirmButtonText(__('ok'))->show();
        }
    }

    public function render()
    {
        return view('pages.buy-card.index', [
            'users' => $this->userService->getAllUsers(
                $this->perPage,
                $this->search,
                $this->selectedRole,
                $this->dateFrom,
                $this->dateTo
            ),
            'roles' => $this->getRoles(),
        ])->layout('layouts.app', [
            'availableLanguages' => Language::all(),
        ]);
    }
}
