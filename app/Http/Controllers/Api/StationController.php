<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\StationService;
use App\Http\Resources\StationResource;
use Illuminate\Http\Request;

class StationController extends Controller
{
    protected StationService $stationService;

    public function __construct(StationService $stationService)
    {
        $this->stationService = $stationService;
    }

    public function index(Request $request)
    {
        $perPage = $request->get('per_page', 15);
        $stations = $this->stationService->getAll($perPage);
        return StationResource::collection($stations);
    }

    public function show($id)
    {
        $station = $this->stationService->findById($id);
        return new StationResource($station);
    }


}