img.logo.logo-lg {
    width: 280px;
    height: 80px;
    background-color: #ddd;
}

html.minimenu .nxl-navigation .logo-sm {
    width: 80px;
    display: block;
    transition: all .3s ease;
    margin: 0px 10px;
}

.nxl-navigation {
    padding-right: 0px !important;
    margin-bottom: 0px;
    list-style: none;
}

.nxl-header {
    right: 280px;
    z-index: 1025;
    position: fixed;
    left: 0;
    color: #eaebef;
    background: #fff;
    min-height: 80px;
    border-bottom: 1px solid #e5e7eb;
}

html.minimenu .nxl-header {
    right: 100px;
}

.ms-auto {
    margin-right: auto !important;
    margin-left: 0 !important;
}

html.minimenu .nxl-header {
    left: 0;
}
.nxl-navigation .navbar-content .nxl-micon {
    margin-right: 12px;
    display: inline-block;
    vertical-align: middle;
}
html.minimenu .nxl-navigation .navbar-content .nxl-caption:before {
    top: 0;
    display: flex;
    align-items: center;
    width: 100%;
    height: 100%;
    font-size: 16px;
    padding-right: 20px;
    padding-left: 0px;
    content: "\e9a4";
    position: absolute;
    font-family: feather !important;
}
html.minimenu .nxl-navigation .navbar-content .nxl-link {
    margin: 0px;
}
.nxl-navigation .navbar-content ul {
    list-style: none;
    padding-right: 0;
    margin-bottom: 0;
}
.nxl-header .header-wrapper .nxl-h-dropdown.dropdown-menu-end {
    left: 0 !important;
    right: auto !important;
}
.breadcrumb-item+.breadcrumb-item::before {
    float: right;
    padding-right: var(--bs-breadcrumb-item-padding-x);
    color: var(--bs-breadcrumb-divider-color);
    content: var(--bs-breadcrumb-divider, "/");
}
.nxl-container {
    position: relative;
    top: 80px;
    margin-right: 280px !important;
    margin-left: 0 !important;
    min-height: calc(100vh - 80px);
    transition: all .3s ease;
}
.modal-header .btn-close {
    padding: calc(var(--bs-modal-header-padding-y) * .5) calc(var(--bs-modal-header-padding-x) * .5);
    margin: 0;
}
